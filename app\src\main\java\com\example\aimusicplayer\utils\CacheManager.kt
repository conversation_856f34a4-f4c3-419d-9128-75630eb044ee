package com.example.aimusicplayer.utils

import android.app.ActivityManager
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import android.util.LruCache
import com.example.aimusicplayer.utils.Constants.CACHE_EXPIRATION_DAYS
import com.example.aimusicplayer.utils.Constants.CACHE_SIZE_MB
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * 缓存管理器
 * 用于管理应用的缓存，包括内存缓存和磁盘缓存
 * 支持动态调整缓存大小和过期时间
 */
class CacheManager(private val context: Context) {
    private val TAG = "CacheManager"

    // 内存缓存
    private val memoryCache: LruCache<String, CacheEntry>

    // 访问频率记录
    private val accessFrequency = ConcurrentHashMap<String, Int>()

    // 缓存目录
    private val cacheDir: File

    // 默认过期时间（毫秒）
    private var defaultExpireTime = TimeUnit.HOURS.toMillis(1) // 默认1小时

    // 最大缓存比例（占可用内存的比例）
    private var maxCacheRatio = 0.125f // 默认1/8

    // 缓存使用统计
    private val cacheStats = CacheStats()

    init {
        // 根据设备性能动态调整缓存大小
        adjustCacheSizeByDevicePerformance()

        // 计算缓存大小
        val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()
        val cacheSize = (maxMemory * maxCacheRatio).toInt()

        memoryCache = object : LruCache<String, CacheEntry>(cacheSize) {
            override fun sizeOf(key: String, value: CacheEntry): Int {
                // 计算缓存对象的大小
                return when (val data = value.data) {
                    is Bitmap -> data.byteCount / 1024
                    is String -> data.length / 1024
                    else -> 1
                }
            }

            override fun entryRemoved(
                evicted: Boolean,
                key: String,
                oldValue: CacheEntry,
                newValue: CacheEntry?
            ) {
                super.entryRemoved(evicted, key, oldValue, newValue)
                // 记录缓存移除事件
                if (evicted) {
                    cacheStats.evictionCount++
                }
            }
        }

        // 初始化缓存目录
        cacheDir = File(context.cacheDir, "music_cache")
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }

        // 清理过期缓存
        cleanExpiredCache()
    }

    /**
     * 根据设备性能动态调整缓存大小
     */
    private fun adjustCacheSizeByDevicePerformance() {
        try {
            // 获取设备总内存
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            val totalMemory = memoryInfo.totalMem

            // 根据总内存调整缓存比例
            maxCacheRatio = when {
                totalMemory >= 6L * 1024 * 1024 * 1024 -> 0.15f  // 6GB以上内存设备
                totalMemory >= 4L * 1024 * 1024 * 1024 -> 0.125f // 4GB以上内存设备
                totalMemory >= 2L * 1024 * 1024 * 1024 -> 0.1f   // 2GB以上内存设备
                else -> 0.08f                                    // 低内存设备
            }

            Log.d(TAG, "设备总内存: ${totalMemory / (1024 * 1024)}MB, 缓存比例: $maxCacheRatio")
        } catch (e: Exception) {
            Log.e(TAG, "调整缓存大小失败", e)
            // 使用默认值
            maxCacheRatio = 0.125f
        }
    }

    /**
     * 将对象放入缓存
     * @param key 缓存键
     * @param value 缓存值
     * @param expireTime 过期时间（毫秒），默认使用全局设置
     */
    fun put(key: String, value: Any, expireTime: Long = defaultExpireTime) {
        val entry = CacheEntry(value, System.currentTimeMillis() + expireTime)
        memoryCache.put(key, entry)

        // 记录访问频率
        accessFrequency[key] = (accessFrequency[key] ?: 0) + 1

        // 更新缓存统计
        cacheStats.putCount++
    }

    /**
     * 从缓存中获取对象
     * @param key 缓存键
     * @return 缓存值，如果不存在或已过期则返回null
     */
    fun get(key: String): Any? {
        val entry = memoryCache.get(key) ?: return null

        // 检查是否过期
        if (entry.isExpired()) {
            memoryCache.remove(key)
            cacheStats.expiredCount++
            return null
        }

        // 记录访问频率并延长过期时间
        val frequency = (accessFrequency[key] ?: 0) + 1
        accessFrequency[key] = frequency

        // 根据访问频率动态调整过期时间
        if (frequency > 10) {
            // 频繁访问的项目延长过期时间
            entry.expireTime = System.currentTimeMillis() + defaultExpireTime * 2
        }

        // 更新缓存统计
        cacheStats.hitCount++

        return entry.data
    }

    /**
     * 将位图保存到磁盘缓存
     * @param key 缓存键
     * @param bitmap 位图
     */
    suspend fun saveBitmapToDisk(key: String, bitmap: Bitmap) {
        withContext(Dispatchers.IO) {
            try {
                val file = File(cacheDir, key.hashCode().toString())
                FileOutputStream(file).use { out ->
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
                }
                Log.d(TAG, "Bitmap saved to disk: $key")
            } catch (e: Exception) {
                Log.e(TAG, "Error saving bitmap to disk", e)
            }
        }
    }

    /**
     * 从磁盘缓存加载位图
     * @param key 缓存键
     * @return 位图，如果不存在则返回null
     */
    suspend fun loadBitmapFromDisk(key: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val file = File(cacheDir, key.hashCode().toString())
                if (file.exists()) {
                    val bitmap = BitmapFactory.decodeFile(file.absolutePath)
                    Log.d(TAG, "Bitmap loaded from disk: $key")
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading bitmap from disk", e)
                null
            }
        }
    }

    /**
     * 将字符串保存到磁盘缓存
     * @param key 缓存键
     * @param value 字符串
     */
    suspend fun saveStringToDisk(key: String, value: String) {
        withContext(Dispatchers.IO) {
            try {
                val file = File(cacheDir, key.hashCode().toString())
                file.writeText(value)
                Log.d(TAG, "String saved to disk: $key")
            } catch (e: Exception) {
                Log.e(TAG, "Error saving string to disk", e)
            }
        }
    }

    /**
     * 从磁盘缓存加载字符串
     * @param key 缓存键
     * @return 字符串，如果不存在则返回null
     */
    suspend fun loadStringFromDisk(key: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                val file = File(cacheDir, key.hashCode().toString())
                if (file.exists()) {
                    val content = file.readText()
                    Log.d(TAG, "String loaded from disk: $key")
                    content
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading string from disk", e)
                null
            }
        }
    }

    /**
     * 清除内存缓存
     */
    fun clearMemoryCache() {
        memoryCache.evictAll()
    }

    /**
     * 清除磁盘缓存
     */
    suspend fun clearDiskCache() {
        withContext(Dispatchers.IO) {
            try {
                cacheDir.listFiles()?.forEach { it.delete() }
                Log.d(TAG, "Disk cache cleared")
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing disk cache", e)
            }
        }
    }

    /**
     * 清理过期缓存
     * 优化版本：使用协程和更智能的清理策略
     */
    private fun cleanExpiredCache() {
        // 使用GlobalScope是安全的，因为这是一个后台清理任务
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val now = System.currentTimeMillis()
                val expirationTime = now - TimeUnit.DAYS.toMillis(CACHE_EXPIRATION_DAYS.toLong())

                // 获取所有缓存文件
                val allFiles = cacheDir.listFiles() ?: return@launch

                // 按照最后修改时间排序
                val sortedFiles = allFiles.sortedBy { it.lastModified() }

                // 统计缓存大小
                val cacheSize = getDiskCacheSize() / (1024 * 1024) // MB
                Log.d(TAG, "当前缓存大小: ${cacheSize}MB")

                // 删除过期文件
                var expiredCount = 0
                sortedFiles.forEach { file ->
                    if (file.lastModified() < expirationTime) {
                        file.delete()
                        expiredCount++
                        Log.d(TAG, "删除过期缓存文件: ${file.name}")
                    }
                }

                // 如果缓存大小仍然超过限制，继续删除最旧的文件
                if (cacheSize > CACHE_SIZE_MB) {
                    val remainingFiles = cacheDir.listFiles()?.sortedBy { it.lastModified() } ?: emptyList()
                    var deletedSize = 0L
                    var deletedCount = 0

                    for (file in remainingFiles) {
                        // 保留最近使用的文件
                        if (deletedCount >= remainingFiles.size / 2) {
                            break
                        }

                        val fileSize = file.length()
                        file.delete()
                        deletedSize += fileSize
                        deletedCount++
                        Log.d(TAG, "删除旧缓存文件以减小缓存大小: ${file.name}")

                        // 如果缓存大小已经降到目标以下，停止删除
                        if ((cacheSize - deletedSize / (1024 * 1024)) <= CACHE_SIZE_MB) {
                            break
                        }
                    }

                    Log.d(TAG, "缓存清理完成，删除了 $deletedCount 个文件，释放了 ${deletedSize / (1024 * 1024)}MB 空间")
                }

                Log.d(TAG, "缓存清理完成，删除了 $expiredCount 个过期文件")

                // 更新最后清理时间
                val prefs = context.getSharedPreferences("cache_prefs", Context.MODE_PRIVATE)
                prefs.edit().putLong("last_cleanup_time", now).apply()

            } catch (e: Exception) {
                Log.e(TAG, "清理缓存时出错", e)
            }
        }
    }

    /**
     * 获取磁盘缓存大小
     * @return 缓存大小（字节）
     */
    private fun getDiskCacheSize(): Long {
        var size = 0L
        cacheDir.listFiles()?.forEach { file ->
            size += file.length()
        }
        return size
    }

    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    suspend fun getCacheStats(): String {
        return withContext(Dispatchers.IO) {
            val memorySize = memoryCache.size() / 1024 // KB
            val diskSize = getDiskCacheSize() / 1024 // KB
            val fileCount = cacheDir.listFiles()?.size ?: 0

            "Memory Cache: $memorySize KB\nDisk Cache: $diskSize KB\nFiles: $fileCount"
        }
    }

    /**
     * 检查是否需要清理缓存
     * 如果上次清理时间超过指定时间，则执行清理
     */
    fun checkAndCleanCache() {
        val prefs = context.getSharedPreferences("cache_prefs", Context.MODE_PRIVATE)
        val lastCleanupTime = prefs.getLong("last_cleanup_time", 0)
        val now = System.currentTimeMillis()

        // 如果上次清理时间超过1天，则执行清理
        if (now - lastCleanupTime > TimeUnit.DAYS.toMillis(1)) {
            Log.d(TAG, "上次缓存清理时间超过1天，执行清理")
            cleanExpiredCache()
        }
    }

    /**
     * 预热缓存 - 预加载常用资源
     */
    fun warmUp() {
        try {
            // 预加载默认专辑封面
            val defaultAlbumArt = BitmapFactory.decodeResource(
                context.resources,
                com.example.aimusicplayer.R.drawable.default_album_art
            )
            if (defaultAlbumArt != null) {
                put("default_album_art", defaultAlbumArt, Long.MAX_VALUE) // 永不过期
            }

            // 预加载常用图标
            val playIcon = BitmapFactory.decodeResource(
                context.resources,
                com.example.aimusicplayer.R.drawable.ic_play
            )
            if (playIcon != null) {
                put("play_icon", playIcon, Long.MAX_VALUE)
            }

            val pauseIcon = BitmapFactory.decodeResource(
                context.resources,
                com.example.aimusicplayer.R.drawable.ic_pause
            )
            if (pauseIcon != null) {
                put("pause_icon", pauseIcon, Long.MAX_VALUE)
            }

            Log.d(TAG, "缓存预热完成")
        } catch (e: Exception) {
            Log.e(TAG, "缓存预热失败", e)
        }
    }

    /**
     * 清理过期缓存（新版本，与现有方法配合使用）
     */
    fun cleanupExpired() {
        try {
            val currentTime = System.currentTimeMillis()
            val keysToRemove = mutableListOf<String>()

            // 检查内存缓存中的过期项
            val snapshot = memoryCache.snapshot()
            for ((key, entry) in snapshot) {
                if (entry.isExpired()) {
                    keysToRemove.add(key)
                }
            }

            // 移除过期项
            for (key in keysToRemove) {
                memoryCache.remove(key)
                accessFrequency.remove(key)
                cacheStats.expiredCount++
            }

            // 同时清理磁盘缓存
            checkAndCleanCache()

            Log.d(TAG, "清理了 ${keysToRemove.size} 个过期内存缓存项")
        } catch (e: Exception) {
            Log.e(TAG, "清理过期缓存失败", e)
        }
    }

    /**
     * 优化缓存性能
     */
    fun optimizeCache() {
        try {
            // 清理低频访问的项目
            val lowFrequencyThreshold = 2
            val keysToRemove = mutableListOf<String>()

            for ((key, frequency) in accessFrequency) {
                if (frequency < lowFrequencyThreshold && memoryCache.get(key) != null) {
                    keysToRemove.add(key)
                }
            }

            // 移除低频访问项（保留最近访问的）
            val maxRemoveCount = keysToRemove.size / 2 // 只移除一半
            for (i in 0 until minOf(maxRemoveCount, keysToRemove.size)) {
                val key = keysToRemove[i]
                memoryCache.remove(key)
                accessFrequency.remove(key)
            }

            Log.d(TAG, "缓存优化完成，移除了 $maxRemoveCount 个低频访问项")
        } catch (e: Exception) {
            Log.e(TAG, "缓存优化失败", e)
        }
    }

    companion object {
        // 单例实例
        @Volatile
        private var INSTANCE: CacheManager? = null

        /**
         * 获取CacheManager实例
         * @param context 上下文
         * @return CacheManager实例
         */
        fun getInstance(context: Context): CacheManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CacheManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
}
