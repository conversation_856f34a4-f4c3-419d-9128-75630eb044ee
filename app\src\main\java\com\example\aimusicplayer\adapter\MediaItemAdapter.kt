package com.example.aimusicplayer.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.media3.common.MediaItem
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.aimusicplayer.R

/**
 * 媒体项适配器
 * 用于显示播放列表中的歌曲
 */
class MediaItemAdapter(
    mediaItems: List<MediaItem>,
    private val onItemClickListener: (Int) -> Unit
) : RecyclerView.Adapter<MediaItemAdapter.ViewHolder>() {

    // 使用可变列表存储媒体项
    private val mediaItems = mediaItems.toMutableList()

    // 长按监听器
    private var onItemLongClickListener: ((Int, MediaItem) -> Unit)? = null

    /**
     * 更新媒体项列表
     * 使用DiffUtil优化列表更新性能
     */
    fun updateMediaItems(newMediaItems: List<MediaItem>) {
        val diffCallback = MediaItemDiffCallback(mediaItems, newMediaItems)
        val diffResult = androidx.recyclerview.widget.DiffUtil.calculateDiff(diffCallback)

        mediaItems.clear()
        mediaItems.addAll(newMediaItems)

        diffResult.dispatchUpdatesTo(this)
    }

    /**
     * 设置长按监听器
     */
    fun setOnItemLongClickListener(listener: (Int, MediaItem) -> Unit) {
        this.onItemLongClickListener = listener
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val coverImageView: ImageView = itemView.findViewById(R.id.image_song_cover)
        val titleTextView: TextView = itemView.findViewById(R.id.text_song_title)
        val artistTextView: TextView = itemView.findViewById(R.id.text_song_artist)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_playlist_song, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val mediaItem = mediaItems[position]

        // 设置标题
        holder.titleTextView.text = mediaItem.mediaMetadata.title ?: "未知歌曲"

        // 设置艺术家
        holder.artistTextView.text = mediaItem.mediaMetadata.artist ?: "未知歌手"

        // 加载封面
        val artworkUri = mediaItem.mediaMetadata.artworkUri
        if (artworkUri != null) {
            Glide.with(holder.coverImageView.context)
                .load(artworkUri)
                .placeholder(R.drawable.default_album_art)
                .error(R.drawable.default_album_art)
                .into(holder.coverImageView)
        } else {
            holder.coverImageView.setImageResource(R.drawable.default_album_art)
        }

        // 设置点击事件
        holder.itemView.setOnClickListener {
            onItemClickListener(position)
        }

        // 设置长按事件
        holder.itemView.setOnLongClickListener {
            onItemLongClickListener?.invoke(position, mediaItem)
            true
        }
    }

    override fun getItemCount(): Int = mediaItems.size

    /**
     * 媒体项DiffCallback
     * 用于计算两个列表的差异，优化列表更新性能
     */
    private class MediaItemDiffCallback(
        private val oldList: List<MediaItem>,
        private val newList: List<MediaItem>
    ) : androidx.recyclerview.widget.DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].mediaId == newList[newItemPosition].mediaId
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]

            return oldItem.mediaMetadata.title == newItem.mediaMetadata.title &&
                   oldItem.mediaMetadata.artist == newItem.mediaMetadata.artist &&
                   oldItem.mediaMetadata.artworkUri == newItem.mediaMetadata.artworkUri
        }
    }
}
