package com.example.aimusicplayer.service

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 播放控制器实现类
 */
@Singleton
class PlayerControllerImpl @Inject constructor(
    private val context: Context
) : PlayerController {
    private val TAG = "PlayerControllerImpl"
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    private val _playlist = MutableLiveData<List<MediaItem>>(emptyList())
    override val playlist: LiveData<List<MediaItem>> = _playlist

    private val _currentSong = MutableLiveData<MediaItem?>(null)
    override val currentSong: LiveData<MediaItem?> = _currentSong

    private val _playState = MutableStateFlow<PlayState>(PlayState.Idle)
    override val playState: StateFlow<PlayState> = _playState

    private val _playProgress = MutableStateFlow(0L)
    override val playProgress: StateFlow<Long> = _playProgress

    private val _bufferingPercent = MutableStateFlow(0)
    override val bufferingPercent: StateFlow<Int> = _bufferingPercent

    private val _playMode = MutableStateFlow<PlayMode>(PlayMode.Loop)
    override val playMode: StateFlow<PlayMode> = _playMode

    private var player: Player? = null
        get() {
            if (field == null) {
                field = PlayServiceModule.getPlayer()
                if (field != null) {
                    initPlayerListener()
                }
            }
            return field
        }

    init {
        // 启动进度更新协程
        scope.launch {
            while (true) {
                updateProgress()
                delay(500)
            }
        }
    }

    private fun updateProgress() {
        player?.let {
            if (it.isPlaying) {
                _playProgress.value = it.currentPosition
                _bufferingPercent.value = it.bufferedPercentage
            }
        }
    }

    private fun initPlayerListener() {
        player?.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_IDLE -> _playState.value = PlayState.Idle
                    Player.STATE_BUFFERING -> _playState.value = PlayState.Preparing
                    Player.STATE_READY -> {
                        if (player?.isPlaying == true) {
                            _playState.value = PlayState.Playing
                        } else {
                            _playState.value = PlayState.Pause
                        }
                    }
                    Player.STATE_ENDED -> next()
                }
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                if (isPlaying) {
                    _playState.value = PlayState.Playing
                } else if (player?.playbackState == Player.STATE_READY) {
                    _playState.value = PlayState.Pause
                }
            }

            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                _currentSong.value = mediaItem
            }
        })
    }

    override fun addAndPlay(song: MediaItem) {
        ensureServiceStarted()
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: mutableListOf()
            currentPlaylist.add(song)
            _playlist.value = currentPlaylist

            it.addMediaItem(song)
            it.prepare()
            it.seekToDefaultPosition(currentPlaylist.size - 1)
            it.play()
        }
    }

    override fun replaceAll(songList: List<MediaItem>, song: MediaItem) {
        ensureServiceStarted()
        player?.let {
            _playlist.value = songList

            it.clearMediaItems()
            it.setMediaItems(songList)
            it.prepare()

            val index = songList.indexOfFirst { item -> item.mediaId == song.mediaId }
            if (index >= 0) {
                it.seekToDefaultPosition(index)
            }
            it.play()
        }
    }

    override fun play(mediaId: String) {
        player?.let {
            val index = _playlist.value?.indexOfFirst { item -> item.mediaId == mediaId } ?: -1
            if (index >= 0) {
                it.seekToDefaultPosition(index)
                it.play()
            }
        }
    }

    override fun delete(song: MediaItem) {
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: return
            val index = currentPlaylist.indexOfFirst { item -> item.mediaId == song.mediaId }
            if (index >= 0) {
                currentPlaylist.removeAt(index)
                _playlist.value = currentPlaylist

                it.removeMediaItem(index)
            }
        }
    }

    override fun clearPlaylist() {
        player?.let {
            _playlist.value = emptyList()
            it.clearMediaItems()
        }
    }

    override fun playPause() {
        ensureServiceStarted()
        player?.let {
            if (it.isPlaying) {
                it.pause()
            } else {
                if (it.playbackState == Player.STATE_IDLE) {
                    it.prepare()
                }
                it.play()
            }
        }
    }

    override fun next() {
        player?.let {
            if (it.hasNextMediaItem()) {
                it.seekToNextMediaItem()
            } else {
                // 如果是列表循环模式，则从头开始播放
                if (_playMode.value == PlayMode.Loop) {
                    it.seekToDefaultPosition(0)
                }
            }
            it.play()
        }
    }

    override fun prev() {
        player?.let {
            if (it.hasPreviousMediaItem()) {
                it.seekToPreviousMediaItem()
            } else {
                // 如果是列表循环模式，则从尾部开始播放
                if (_playMode.value == PlayMode.Loop) {
                    val size = _playlist.value?.size ?: 0
                    if (size > 0) {
                        it.seekToDefaultPosition(size - 1)
                    }
                }
            }
            it.play()
        }
    }

    override fun seekTo(msec: Int) {
        player?.seekTo(msec.toLong())
    }

    override fun getAudioSessionId(): Int {
        // 使用反射获取audioSessionId
        return try {
            val player = player ?: return 0
            val method = player.javaClass.getMethod("getAudioSessionId")
            method.invoke(player) as? Int ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "获取audioSessionId失败", e)
            0
        }
    }

    override fun setPlayMode(mode: PlayMode) {
        _playMode.value = mode
        player?.let {
            when (mode) {
                PlayMode.Loop -> {
                    it.repeatMode = Player.REPEAT_MODE_ALL
                    it.shuffleModeEnabled = false
                }
                PlayMode.Shuffle -> {
                    it.repeatMode = Player.REPEAT_MODE_ALL
                    it.shuffleModeEnabled = true
                }
                PlayMode.Single -> {
                    it.repeatMode = Player.REPEAT_MODE_ONE
                    it.shuffleModeEnabled = false
                }
            }
        }
    }

    override fun stop() {
        player?.stop()
    }

    override fun shufflePlaylist() {
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: return
            if (currentPlaylist.size <= 1) return

            // 获取当前播放的歌曲
            val currentIndex = it.currentMediaItemIndex
            val currentSong = if (currentIndex >= 0 && currentIndex < currentPlaylist.size) {
                currentPlaylist[currentIndex]
            } else null

            // 移除当前播放的歌曲
            if (currentSong != null) {
                currentPlaylist.removeAt(currentIndex)
            }

            // 随机打乱剩余歌曲
            currentPlaylist.shuffle()

            // 将当前播放的歌曲放在第一位
            if (currentSong != null) {
                currentPlaylist.add(0, currentSong)
            }

            // 更新播放列表
            _playlist.value = currentPlaylist
            it.clearMediaItems()
            it.setMediaItems(currentPlaylist)
            it.prepare()

            // 播放第一首（当前歌曲）
            if (currentSong != null) {
                it.seekToDefaultPosition(0)
                it.play()
            }
        }
    }

    override fun removeFromPlaylist(position: Int) {
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: return
            if (position < 0 || position >= currentPlaylist.size) return

            // 移除指定位置的歌曲
            currentPlaylist.removeAt(position)
            _playlist.value = currentPlaylist

            // 从播放器中移除
            it.removeMediaItem(position)
        }
    }

    override fun getCurrentPlaylist(): List<MediaItem> {
        return _playlist.value ?: emptyList()
    }

    override fun getCurrentIndex(): Int {
        return player?.currentMediaItemIndex ?: -1
    }

    override fun getCurrentPosition(): Long {
        return player?.currentPosition ?: 0
    }

    override fun getDuration(): Long {
        return player?.duration ?: 0
    }

    override fun playAtIndex(index: Int) {
        player?.let {
            if (index >= 0 && index < (_playlist.value?.size ?: 0)) {
                it.seekToDefaultPosition(index)
                it.play()
            }
        }
    }

    private fun ensureServiceStarted() {
        try {
            val intent = Intent(context, UnifiedPlaybackService::class.java)
            context.startService(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start service", e)
        }
    }
}
