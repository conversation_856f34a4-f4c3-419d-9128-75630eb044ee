> Task :app:compileDebugKotlin
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/MusicApplication.kt:72:17 Unresolved reference: BuildConfig
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/MusicApplication.kt:106:13 Unresolved reference: BuildConfig
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:76:67 Type mismatch: inferred type is Flow<NetworkResult<Unit>> but Flow<NetworkResult<Song?>> was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:76:79 Type mismatch: inferred type is Unit but Song? was expected
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:77:25 Overload resolution ambiguity: 
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:86:32 Overload resolution ambiguity: 
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:92:5 Conflicting overloads: public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource, public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:107:5 Conflicting overloads: public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource, public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:216:34 Overload resolution ambiguity: 
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:375:34 Overload resolution ambiguity: 
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:619:42 Overload resolution ambiguity: 
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
public final suspend fun getSongDetail(id: Long): Song? defined in com.example.aimusicplayer.data.source.MusicDataSource
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:54:44 Unresolved reference: LyricAdapter
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:55:44 Unresolved reference: EmptyLyricAdapter
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:325:72 Unresolved reference: Uri
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:435:75 Unresolved reference: lyric_view
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:459:75 Unresolved reference: lyric_view
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:670:28 Unresolved reference: LyricAdapter
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:679:41 Overload resolution ambiguity: 
public open fun `<Error function>`(): [Error type: Return type for function cannot be resolved] defined in root package
public abstract fun getItemCount(): Int defined in androidx.recyclerview.widget.RecyclerView.Adapter
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:680:53 Overload resolution ambiguity: 
public final operator fun times(other: Byte): Float defined in kotlin.Float
public final operator fun times(other: Double): Double defined in kotlin.Float
public final operator fun times(other: Float): Float defined in kotlin.Float
public final operator fun times(other: Int): Float defined in kotlin.Float
public final operator fun times(other: Long): Float defined in kotlin.Float
public final operator fun times(other: Short): Float defined in kotlin.Float
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:711:40 Unresolved reference: LyricAdapter
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:740:44 Unresolved reference: LyricAdapter
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:771:58 Suspend function 'parseLrc' should be called only from a coroutine or another suspend function
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:772:32 Unresolved reference: lyricLines
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:772:49 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:782:31 Unresolved reference: lyricLines
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:782:48 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:806:67 Unresolved reference: lyric_view
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1185:64 Unresolved reference: button_clear_playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1186:66 Unresolved reference: button_shuffle_playlist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1196:17 Unresolved reference: setOnItemLongClickListener
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1196:46 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1196:56 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1231:27 Unresolved reference: clearPlaylist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1238:23 Unresolved reference: shufflePlaylist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:1262:27 Unresolved reference: removeFromPlaylist
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt:397:46 Unresolved reference: Song

> Task :app:compileDebugKotlin FAILED

