package com.example.aimusicplayer

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.multidex.MultiDexApplication
import com.example.aimusicplayer.utils.Constants
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

/**
 * 应用程序类
 * 用于初始化Hilt和其他全局配置
 * 提供全局应用上下文和用户Cookie管理
 */
@HiltAndroidApp
class MusicApplication : MultiDexApplication() {

    companion object {
        private const val TAG = "MusicApplication"
        private lateinit var instance: MusicApplication

        /**
         * 获取应用实例
         * @return 应用实例
         */
        @JvmStatic
        fun getInstance(): MusicApplication = instance

        /**
         * 获取应用上下文
         * @return 应用上下文
         */
        fun getContext(): Context = instance
    }

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override fun onCreate() {
        super.onCreate()

        // 保存静态实例
        instance = this

        // 启动性能优化 - 延迟非关键初始化
        initializeCriticalComponents()

        // 使用后台线程初始化非关键组件
        Thread {
            initializeNonCriticalComponents()
        }.start()

        Log.d(TAG, "应用启动初始化完成")
    }

    /**
     * 初始化关键组件（主线程）
     */
    private fun initializeCriticalComponents() {
        try {
            // 设置夜间模式（必须在主线程）
            val isNightMode = sharedPreferences.getBoolean(Constants.PREF_NIGHT_MODE, false)
            if (isNightMode) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            }

            // 启用严格模式（仅在调试模式下）
            if (com.example.aimusicplayer.BuildConfig.DEBUG) {
                enableStrictMode()
            }

            Log.d(TAG, "关键组件初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "关键组件初始化失败", e)
        }
    }

    /**
     * 初始化非关键组件（后台线程）
     */
    private fun initializeNonCriticalComponents() {
        try {
            // 预热缓存
            warmUpCaches()

            // 初始化图片加载器
            initializeImageLoader()

            // 清理过期缓存
            cleanupExpiredCaches()

            Log.d(TAG, "非关键组件初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "非关键组件初始化失败", e)
        }
    }

    /**
     * 启用严格模式（调试模式）
     */
    private fun enableStrictMode() {
        if (com.example.aimusicplayer.BuildConfig.DEBUG) {
            android.os.StrictMode.setThreadPolicy(
                android.os.StrictMode.ThreadPolicy.Builder()
                    .detectDiskReads()
                    .detectDiskWrites()
                    .detectNetwork()
                    .penaltyLog()
                    .build()
            )

            android.os.StrictMode.setVmPolicy(
                android.os.StrictMode.VmPolicy.Builder()
                    .detectLeakedSqlLiteObjects()
                    .detectLeakedClosableObjects()
                    .penaltyLog()
                    .build()
            )
        }
    }

    /**
     * 预热缓存
     */
    private fun warmUpCaches() {
        try {
            // 预热图片缓存
            val cacheManager = com.example.aimusicplayer.utils.CacheManager.getInstance(this)
            cacheManager.warmUp()

            Log.d(TAG, "缓存预热完成")
        } catch (e: Exception) {
            Log.e(TAG, "缓存预热失败", e)
        }
    }

    /**
     * 初始化图片加载器
     */
    private fun initializeImageLoader() {
        try {
            // 预加载Glide
            com.bumptech.glide.Glide.get(this)
            Log.d(TAG, "图片加载器初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "图片加载器初始化失败", e)
        }
    }

    /**
     * 清理过期缓存
     */
    private fun cleanupExpiredCaches() {
        try {
            val cacheManager = com.example.aimusicplayer.utils.CacheManager.getInstance(this)
            cacheManager.cleanupExpired()
            Log.d(TAG, "过期缓存清理完成")
        } catch (e: Exception) {
            Log.e(TAG, "过期缓存清理失败", e)
        }
    }

    /**
     * 保存用户Cookie
     * @param cookie 用户登录Cookie
     */
    fun saveCookie(cookie: String?) {
        if (cookie.isNullOrEmpty()) {
            Log.w(TAG, "尝试保存空Cookie")
            return
        }

        // 记录cookie信息（隐藏敏感信息）
        val logCookie = if (cookie.length > 20) {
            cookie.substring(0, 10) + "..." + cookie.substring(cookie.length - 10)
        } else {
            cookie
        }
        Log.d(TAG, "保存Cookie: $logCookie")

        sharedPreferences.edit().apply {
            putString(Constants.PREF_COOKIE, cookie)
            apply()
        }

        // 验证保存是否成功
        val savedCookie = sharedPreferences.getString(Constants.PREF_COOKIE, "")
        if (!savedCookie.isNullOrEmpty()) {
            Log.d(TAG, "Cookie已成功保存")
        } else {
            Log.e(TAG, "Cookie保存失败，保存后读取为空")
        }
    }

    /**
     * 获取用户Cookie
     * @return 保存的Cookie，如果没有则返回空字符串
     */
    fun getCookie(): String {
        val cookie = sharedPreferences.getString(Constants.PREF_COOKIE, "") ?: ""
        if (cookie.isEmpty()) {
            Log.w(TAG, "获取到的Cookie为空")
        } else {
            val logCookie = if (cookie.length > 20) {
                cookie.substring(0, 10) + "..." + cookie.substring(cookie.length - 10)
            } else {
                cookie
            }
            Log.d(TAG, "获取到Cookie: $logCookie")
        }
        return cookie
    }

    /**
     * 清除用户Cookie
     */
    fun clearCookie() {
        sharedPreferences.edit().apply {
            remove(Constants.PREF_COOKIE)
            apply()
        }
        Log.d(TAG, "Cookie已清除")
    }
}
